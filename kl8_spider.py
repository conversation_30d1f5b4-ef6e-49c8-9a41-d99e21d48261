#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快乐8数据爬虫
支持获取快乐8开奖数据
"""

import requests
import re
from typing import List, Dict, Optional
from bs4 import BeautifulSoup
import time


class KL8LotteryData:
    """快乐8开奖数据模型"""
    
    def __init__(self, period: str, date: str, time: str, numbers: List[int], 
                 bet_amount: str = "", prize_pool: str = ""):
        self.period = period  # 期号
        self.date = date      # 开奖日期
        self.time = time      # 开奖时间
        self.numbers = numbers  # 开奖号码
        self.bet_amount = bet_amount  # 投注总额
        self.prize_pool = prize_pool  # 奖池金额
        
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'period': self.period,
            'date': self.date,
            'time': self.time,
            'numbers': self.numbers,
            'bet_amount': self.bet_amount,
            'prize_pool': self.prize_pool,
            'datetime': f"{self.date} {self.time}"
        }


class KL8Spider:
    """快乐8爬虫类"""
    
    def __init__(self):
        self.base_url = "https://www.917500.cn/win/getlist.html"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def get_lottery_data(self, page: int = 1, limit: int = 20, ish: int = 0) -> List[KL8LotteryData]:
        """
        获取快乐8开奖数据
        
        Args:
            page: 页码，默认1
            limit: 每页数量，默认20
            ish: 参数，默认0
            
        Returns:
            List[KL8LotteryData]: 开奖数据列表
        """
        try:
            # 构建请求参数
            data = {
                'lotid': 'kl8',
                'page': page,
                'limit': limit,
                'ish': ish
            }
            
            # 发送POST请求
            response = self.session.post(self.base_url, data=data, timeout=10)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            # 解析HTML数据
            return self._parse_html(response.text)
            
        except requests.RequestException as e:
            print(f"请求失败: {e}")
            return []
        except Exception as e:
            print(f"解析失败: {e}")
            return []
    
    def _parse_html(self, html_content: str) -> List[KL8LotteryData]:
        """解析HTML内容，提取开奖数据"""
        lottery_data = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找所有开奖数据容器
            kjbox_divs = soup.find_all('div', class_='kjbox1')
            
            for kjbox in kjbox_divs:
                try:
                    # 提取期号和时间信息
                    h1_tag = kjbox.find('p', class_='h1')
                    if not h1_tag:
                        continue
                        
                    # 提取期号
                    period_tag = h1_tag.find('b')
                    period = period_tag.text.strip() if period_tag else ""
                    
                    # 提取日期时间
                    time_tag = h1_tag.find('i')
                    datetime_str = time_tag.text.strip() if time_tag else ""
                    
                    # 解析日期和时间
                    date_part, time_part = self._parse_datetime(datetime_str)
                    
                    # 提取开奖号码
                    kl8_tag = kjbox.find('p', class_='kl8')
                    numbers = []
                    if kl8_tag:
                        number_tags = kl8_tag.find_all('b')
                        numbers = [int(tag.text.strip()) for tag in number_tags if tag.text.strip().isdigit()]
                    
                    # 提取投注总额和奖池金额
                    bet_amount = ""
                    prize_pool = ""
                    bottom_div = kjbox.find('div', class_='bottom')
                    if bottom_div:
                        text_content = bottom_div.get_text()
                        # 使用正则表达式提取金额信息
                        bet_match = re.search(r'投注总额：([\d,]+\.?\d*)元', text_content)
                        pool_match = re.search(r'奖池金额：([\d,]+\.?\d*)元', text_content)
                        
                        if bet_match:
                            bet_amount = bet_match.group(1)
                        if pool_match:
                            prize_pool = pool_match.group(1)
                    
                    # 创建数据对象
                    if period and numbers:
                        lottery_item = KL8LotteryData(
                            period=period,
                            date=date_part,
                            time=time_part,
                            numbers=numbers,
                            bet_amount=bet_amount,
                            prize_pool=prize_pool
                        )
                        lottery_data.append(lottery_item)
                        
                except Exception as e:
                    print(f"解析单条数据失败: {e}")
                    continue
                    
        except Exception as e:
            print(f"HTML解析失败: {e}")
            
        return lottery_data
    
    def _parse_datetime(self, datetime_str: str) -> tuple:
        """解析日期时间字符串"""
        try:
            # 格式如: "2025-04-08 21:30"
            if ' ' in datetime_str:
                date_part, time_part = datetime_str.split(' ', 1)
                return date_part.strip(), time_part.strip()
            else:
                return datetime_str.strip(), ""
        except:
            return "", ""
    
    def get_latest_data(self, count: int = 10) -> List[KL8LotteryData]:
        """获取最新的开奖数据"""
        return self.get_lottery_data(page=1, limit=count)
    
    def search_by_period(self, target_period: str, max_pages: int = 10) -> Optional[KL8LotteryData]:
        """根据期号搜索开奖数据"""
        for page in range(1, max_pages + 1):
            data_list = self.get_lottery_data(page=page, limit=20)
            for data in data_list:
                if data.period == target_period:
                    return data
            time.sleep(0.5)  # 避免请求过快
        return None


def main():
    """测试函数"""
    spider = KL8Spider()
    
    print("=== 快乐8数据爬虫测试 ===")
    
    # 获取最新数据
    print("\n1. 获取最新5期数据:")
    latest_data = spider.get_latest_data(5)
    
    for i, data in enumerate(latest_data, 1):
        print(f"{i}. 期号: {data.period}")
        print(f"   时间: {data.date} {data.time}")
        print(f"   号码: {data.numbers}")
        print(f"   投注总额: {data.bet_amount}元")
        print(f"   奖池金额: {data.prize_pool}元")
        print()
    
    # 搜索特定期号
    if latest_data:
        target_period = latest_data[0].period
        print(f"2. 搜索期号 {target_period}:")
        found_data = spider.search_by_period(target_period)
        if found_data:
            print(f"   找到数据: {found_data.to_dict()}")
        else:
            print("   未找到数据")


if __name__ == "__main__":
    main()
