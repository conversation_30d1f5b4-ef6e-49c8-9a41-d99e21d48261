# 快乐8数据爬虫

一个用于爬取快乐8开奖数据的Python工具，提供了爬虫类和RESTful API接口。

## 功能特性

- 🎯 **数据爬取**: 从官方网站爬取快乐8开奖数据
- 📊 **数据解析**: 自动解析HTML，提取期号、时间、号码等信息
- 🔍 **灵活查询**: 支持最新数据获取、分页查询、期号搜索
- 🌐 **API接口**: 提供RESTful API服务
- 📈 **数据分析**: 内置号码频率分析功能
- 💾 **数据导出**: 支持JSON格式数据导出

## 项目结构

```
├── kl8_spider.py      # 核心爬虫类
├── api_server.py      # Flask API服务器
├── test_spider.py     # 测试脚本
├── example_usage.py   # 使用示例
├── requirements.txt   # 依赖包
└── README.md         # 说明文档
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

### 1. 直接使用爬虫

```python
from kl8_spider import KL8Spider

# 创建爬虫实例
spider = KL8Spider()

# 获取最新10期数据
data = spider.get_latest_data(10)

for item in data:
    print(f"期号: {item.period}")
    print(f"时间: {item.date} {item.time}")
    print(f"号码: {item.numbers}")
    print()
```

### 2. 启动API服务器

```bash
python api_server.py
```

服务器启动后访问: http://localhost:5000

### 3. 使用API接口

```python
import requests

# 获取最新数据
response = requests.get('http://localhost:5000/api/latest/5')
data = response.json()

print(f"获取到 {data['count']} 期数据")
for item in data['data']:
    print(f"期号: {item['period']}, 号码: {item['numbers']}")
```

## API接口文档

### 基础信息

- **Base URL**: `http://localhost:5000`
- **数据格式**: JSON
- **字符编码**: UTF-8

### 接口列表

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/` | API首页和接口列表 |
| GET | `/api/health` | 健康检查 |
| GET | `/api/latest` | 获取最新10期数据 |
| GET | `/api/latest/<count>` | 获取最新N期数据 |
| GET | `/api/period/<period>` | 根据期号查询数据 |
| GET | `/api/data` | 分页获取数据 |
| GET | `/api/numbers/analysis` | 号码频率分析 |

### 接口详情

#### 1. 获取最新数据

```
GET /api/latest/5
```

**响应示例:**
```json
{
  "success": true,
  "count": 5,
  "data": [
    {
      "period": "2025088",
      "date": "2025-04-08",
      "time": "21:30",
      "numbers": [3, 4, 5, 7, 10, 18, 20, 24, 25, 36, 45, 47, 50, 53, 57, 63, 65, 70, 71, 75],
      "bet_amount": "122,980,064.00",
      "prize_pool": "102,121,804.05",
      "datetime": "2025-04-08 21:30"
    }
  ],
  "timestamp": "2025-01-08T10:30:00"
}
```

#### 2. 期号查询

```
GET /api/period/2025088
```

#### 3. 分页查询

```
GET /api/data?page=1&limit=20&ish=0
```

**参数说明:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20, 最大: 50)
- `ish`: 查询参数 (默认: 0)

#### 4. 号码分析

```
GET /api/numbers/analysis?count=50
```

**响应示例:**
```json
{
  "success": true,
  "analysis": {
    "total_draws": 50,
    "total_numbers": 80,
    "frequency": [
      {
        "number": 15,
        "count": 32,
        "percentage": 64.0
      }
    ],
    "hot_numbers": [15, 23, 45, 67, 12],
    "cold_numbers": [2, 78, 34, 56, 89]
  }
}
```

## 数据模型

### KL8LotteryData

```python
class KL8LotteryData:
    def __init__(self, period, date, time, numbers, bet_amount="", prize_pool=""):
        self.period = period        # 期号 (str)
        self.date = date           # 开奖日期 (str)
        self.time = time           # 开奖时间 (str)
        self.numbers = numbers     # 开奖号码 (List[int])
        self.bet_amount = bet_amount   # 投注总额 (str)
        self.prize_pool = prize_pool   # 奖池金额 (str)
```

## 使用示例

### 示例1: 基本数据获取

```python
from kl8_spider import KL8Spider

spider = KL8Spider()

# 获取最新5期
data = spider.get_latest_data(5)
for item in data:
    print(f"期号: {item.period}, 号码: {item.numbers}")

# 分页获取
data = spider.get_lottery_data(page=2, limit=10)
print(f"第2页获取到 {len(data)} 期数据")
```

### 示例2: 数据分析

```python
# 获取30期数据进行分析
data = spider.get_latest_data(30)

# 统计号码频率
number_freq = {}
for item in data:
    for num in item.numbers:
        number_freq[num] = number_freq.get(num, 0) + 1

# 找出热门号码
hot_numbers = sorted(number_freq.items(), key=lambda x: x[1], reverse=True)[:10]
print("热门号码:", [num for num, freq in hot_numbers])
```

### 示例3: 搜索特定期号

```python
# 搜索指定期号
target_period = "2025088"
found_data = spider.search_by_period(target_period)

if found_data:
    print(f"找到期号 {target_period} 的数据")
    print(f"开奖号码: {found_data.numbers}")
else:
    print("未找到数据")
```

## 测试

运行测试脚本:

```bash
python test_spider.py
```

运行使用示例:

```bash
python example_usage.py
```

## 注意事项

1. **请求频率**: 建议在请求间添加适当延时，避免对服务器造成压力
2. **错误处理**: 网络请求可能失败，请做好异常处理
3. **数据时效**: 开奖数据通常在每天21:30后更新
4. **合规使用**: 请遵守网站使用条款，仅用于学习和研究目的

## 错误处理

```python
try:
    data = spider.get_latest_data(10)
    if data:
        print(f"成功获取 {len(data)} 期数据")
    else:
        print("未获取到数据")
except Exception as e:
    print(f"获取数据失败: {e}")
```

## 许可证

本项目仅供学习和研究使用，请勿用于商业用途。

## 更新日志

- **v1.0.0** (2025-01-08)
  - 初始版本发布
  - 支持基本数据爬取
  - 提供RESTful API接口
  - 内置数据分析功能