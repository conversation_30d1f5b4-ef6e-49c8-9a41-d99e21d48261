#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快乐8爬虫使用示例
"""

from kl8_spider import KL8Spider
import json
from datetime import datetime

def example_5_save_to_file():
    """示例5: 保存数据到文件"""
    print("=== 示例5: 保存数据到文件 ===")
    
    spider = KL8Spider()
    
    # 获取数据
    data = spider.get_latest_data(100)
    
    if not data:
        print("未获取到数据")
        return
    
    # 转换为字典格式
    data_dict = [item.to_dict() for item in data]
    
    # 保存为JSON文件
    filename = f"kl8_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'count': len(data_dict),
                'data': data_dict
            }, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 数据已保存到文件: {filename}")
        print(f"共保存 {len(data_dict)} 期数据")
        
    except Exception as e:
        print(f"✗ 保存文件失败: {e}")


if __name__ == "__main__":
    # main()
    example_5_save_to_file()
